<!-- ============================================ -->
<!--                   Hero                       -->
<!-- ============================================ -->
---
import myPhoto from "../assets/herosectionimage.png";
---
<section id="hero-2436">
    <div class="cs-container">
        <div class="cs-brand-header">
            <h1 class="cs-brand-name">Pureluxus</h1>
        </div>
        <div class="cs-content">
            <div class="cs-text-content">
                <span class="cs-topper">Welcome to Pureluxus</span>
                <h2 class="cs-title">Luxury Beauty, Redefined</h2>
                <p class="cs-text">
                    Experience the pinnacle of skincare excellence with our scientifically-advanced, botanically-enriched formulations designed to reveal your most radiant, luminous complexion.
                </p>
                <a href="/productoverview" class="cs-button-solid">Discover Our Collection</a>
            </div>
        </div>
    </div>
    <!--Background Image-->
    <picture class="cs-background">
        <!--Mobile Image-->
        <source media="(max-width: 600px)" srcset={myPhoto.src}/>
        <!--Tablet and above Image-->
        <source media="(min-width: 601px)" srcset={myPhoto.src}/>
        <img decoding="async" src={myPhoto.src} alt="luxury beauty products" width="1800" height="860" aria-hidden="true" fetchpriority="high">
    </picture>
</section>

<style>/*-- -------------------------- -->
<---           Hero             -->
<--- -------------------------- -*/

/* Mobile */
@media only screen and (min-width: 0rem) {
  #hero-2436 {
    min-height: 100vh;
    padding: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
  }
  #hero-2436:before {
    content: '';
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 154, 139, 0.15) 0%, rgba(244, 166, 205, 0.15) 100%);
    opacity: 1;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
  #hero-2436:after {
    content: '';
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(45, 27, 27, 0.3) 0%, rgba(45, 27, 27, 0.6) 100%);
    opacity: 1;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
  #hero-2436 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: 0 auto;
    padding: clamp(2rem, 5vw, 4rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    gap: clamp(2rem, 5vw, 4rem);
  }

  #hero-2436 .cs-brand-header {
    text-align: center;
    margin-bottom: clamp(1rem, 3vw, 2rem);
  }

  #hero-2436 .cs-brand-name {
    font-family: 'Playfair Display', serif;
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 700;
    letter-spacing: -0.02em;
    color: var(--bodyTextColorWhite);
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  #hero-2436 .cs-content {
    text-align: center;
    width: 100%;
    max-width: 50rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  #hero-2436 .cs-text-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(1rem, 2.5vw, 1.5rem);
  }
  #hero-2436 .cs-topper {
    font-family: 'Cormorant Garamond', serif;
    font-size: clamp(0.875rem, 2vw, 1.125rem);
    font-weight: 600;
    font-style: italic;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    color: var(--primaryLight);
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

  #hero-2436 .cs-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(2.5rem, 7vw, 4.5rem);
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.02em;
    color: var(--bodyTextColorWhite);
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
  }

  #hero-2436 .cs-text {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    font-weight: 400;
    line-height: 1.6;
    color: var(--bodyTextColorWhite);
    margin: 0;
    max-width: 40rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

  #hero-2436 .cs-button-solid {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 2.5rem;
    margin-top: 1rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primaryLight) 100%);
    color: var(--bodyTextColorWhite);
    border: 2px solid transparent;
    border-radius: 50px;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 154, 139, 0.3);
  }

  #hero-2436 .cs-button-solid:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 154, 139, 0.4);
    background: linear-gradient(135deg, var(--primaryLight) 0%, var(--primary) 100%);
  }
  #hero-2436 .cs-background {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
    z-index: -2;
  }

  #hero-2436 .cs-background img {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #hero-2436 .cs-container {
    padding: clamp(3rem, 6vw, 5rem);
  }

  #hero-2436 .cs-brand-name {
    font-size: clamp(3rem, 7vw, 4.5rem);
  }

  #hero-2436 .cs-title {
    font-size: clamp(3rem, 8vw, 5rem);
  }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #hero-2436 {
    align-items: center;
  }

  #hero-2436 .cs-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: clamp(3rem, 6vw, 5rem);
  }

  #hero-2436 .cs-content {
    max-width: 60rem;
  }

  #hero-2436 .cs-text-content {
    gap: clamp(1.5rem, 3vw, 2.5rem);
  }

  #hero-2436 .cs-brand-name {
    font-size: clamp(3.5rem, 8vw, 5rem);
  }

  #hero-2436 .cs-title {
    font-size: clamp(3.5rem, 9vw, 6rem);
  }

  #hero-2436 .cs-text {
    font-size: clamp(1.125rem, 2.5vw, 1.375rem);
    max-width: 45rem;
  }

  #hero-2436 .cs-button-solid {
    font-size: 1.125rem;
    padding: 1.25rem 3rem;
    margin-top: 1.5rem;
  }
}
                                </style>