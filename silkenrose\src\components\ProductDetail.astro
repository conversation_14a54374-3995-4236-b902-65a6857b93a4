---
const { product } = Astro.props;
---

<section id="product-detail">
    <div class="cs-container">
        <!-- Breadcrumb -->
        <div class="cs-breadcrumb">
            <a href="/" class="cs-breadcrumb-link">Home</a>
            <span class="cs-breadcrumb-separator">/</span>
            <a href="/productoverview" class="cs-breadcrumb-link">Products</a>
            <span class="cs-breadcrumb-separator">/</span>
            <span class="cs-breadcrumb-current">{product.name}</span>
        </div>

        <div class="cs-content">
            <!-- Product Images -->
            <div class="cs-image-section">
                <div class="cs-main-image">
                    <picture class="cs-picture">
                        <source media="(max-width: 600px)" srcset={product.images.mobile}>
                        <source media="(min-width: 601px)" srcset={product.images.main}>
                        <img loading="lazy" decoding="async" src={product.images.main} alt={product.name} width="600" height="600">
                    </picture>
                </div>
                <div class="cs-image-gallery">
                    {product.images.gallery.map((image, index) => (
                        <div class="cs-gallery-item">
                            <img src={image} alt={`${product.name} view ${index + 1}`} width="120" height="120">
                        </div>
                    ))}
                </div>
            </div>

            <!-- Product Information -->
            <div class="cs-info-section">
                <div class="cs-category">{product.category}</div>
                <h1 class="cs-product-title">{product.name}</h1>
                <div class="cs-price">{product.price}</div>
                
                <div class="cs-short-description">
                    <p>{product.shortDescription}</p>
                </div>

                <div class="cs-actions">
                    <button class="cs-button-solid cs-add-to-cart">Add to Cart</button>
                    <button class="cs-button-outline cs-wishlist">Add to Wishlist</button>
                </div>

                <!-- Product Details Tabs -->
                <div class="cs-tabs">
                    <div class="cs-tab-buttons">
                        <button class="cs-tab-button active" data-tab="description">Description</button>
                        <button class="cs-tab-button" data-tab="ingredients">Ingredients</button>
                        <button class="cs-tab-button" data-tab="benefits">Benefits</button>
                        <button class="cs-tab-button" data-tab="usage">How to Use</button>
                    </div>

                    <div class="cs-tab-content">
                        <div class="cs-tab-panel active" id="description">
                            <p class="cs-description">{product.longDescription}</p>
                        </div>

                        <div class="cs-tab-panel" id="ingredients">
                            <h3>Key Ingredients</h3>
                            <ul class="cs-ingredient-list">
                                {product.keyIngredients.map((ingredient) => (
                                    <li>{ingredient}</li>
                                ))}
                            </ul>
                        </div>

                        <div class="cs-tab-panel" id="benefits">
                            <h3>Benefits</h3>
                            <ul class="cs-benefit-list">
                                {product.benefits.map((benefit) => (
                                    <li>{benefit}</li>
                                ))}
                            </ul>
                        </div>

                        <div class="cs-tab-panel" id="usage">
                            <h3>How to Use</h3>
                            <p>{product.howToUse}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Product Detail Styles */
#product-detail {
    padding: var(--sectionPadding);
    padding-top: 12rem; /* Account for fixed navigation with generous spacing */
}

#product-detail .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2rem;
}

/* Breadcrumb */
.cs-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--bodyTextColor);
}

.cs-breadcrumb-link {
    color: var(--bodyTextColor);
    text-decoration: none;
    transition: color 0.3s;
}

.cs-breadcrumb-link:hover {
    color: var(--primary);
}

.cs-breadcrumb-separator {
    color: var(--bodyTextColor);
    opacity: 0.6;
}

.cs-breadcrumb-current {
    color: var(--primary);
    font-weight: 500;
}

/* Content Layout */
.cs-content {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
}

/* Image Section */
.cs-image-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cs-main-image {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.cs-main-image .cs-picture {
    display: block;
    width: 100%;
    height: auto;
}

.cs-main-image img {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cs-image-gallery {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cs-gallery-item {
    width: 120px;
    height: 120px;
    border-radius: 0.5rem;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.cs-gallery-item:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.cs-gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Info Section */
.cs-info-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cs-category {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.cs-product-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: var(--headerColor);
    margin: 0;
}

.cs-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
}

.cs-short-description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--bodyTextColor);
}

/* Actions */
.cs-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* Enhanced Add to Cart Button */
.cs-button-solid {
    font-family: 'Poppins', sans-serif;
    font-size: 1.125rem;
    font-weight: 700;
    line-height: 1;
    text-decoration: none;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    min-width: 12rem;
    margin: 0;
    box-sizing: border-box;
    padding: 1.25rem 2rem;
    color: #fff;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primaryLight) 100%);
    border: none;
    border-radius: 50px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    z-index: 1;
    cursor: pointer;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(255, 154, 139, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.cs-button-solid:before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primaryLight) 0%, var(--secondary) 100%);
    transition: left 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: -1;
}

.cs-button-solid:after {
    content: "🛒";
    font-size: 1rem;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
}

.cs-button-solid:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 154, 139, 0.4);
    color: #fff;
}

.cs-button-solid:hover:before {
    left: 0;
}

.cs-button-solid:hover:after {
    opacity: 1;
    transform: translateX(0);
}

.cs-button-solid:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(255, 154, 139, 0.3);
}

/* Add subtle animation */
@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(255, 154, 139, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(255, 154, 139, 0.5);
    }
    100% {
        box-shadow: 0 4px 15px rgba(255, 154, 139, 0.3);
    }
}

.cs-button-solid.cs-add-to-cart {
    animation: pulse 3s ease-in-out infinite;
}

.cs-button-outline {
    font-size: 1rem;
    font-weight: 600;
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    text-align: center;
    min-width: 9.375rem;
    margin: 0;
    box-sizing: border-box;
    padding: 0 1.5rem;
    color: var(--primary);
    background-color: transparent;
    border: 2px solid var(--primary);
    border-radius: 50px;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    cursor: pointer;
}

.cs-button-outline:hover {
    color: #fff;
    background-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 154, 139, 0.2);
}

/* Tabs */
.cs-tabs {
    width: 100%;
    margin-top: 2rem;
}

.cs-tab-buttons {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    gap: 0;
    flex-wrap: wrap;
}

.cs-tab-button {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    color: var(--bodyTextColor);
    cursor: pointer;
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
}

.cs-tab-button.active,
.cs-tab-button:hover {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

.cs-tab-content {
    padding: 2rem 0;
}

.cs-tab-panel {
    display: none;
}

.cs-tab-panel.active {
    display: block;
}

.cs-tab-panel h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--headerColor);
    margin: 0 0 1rem 0;
}

.cs-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--bodyTextColor);
}

.cs-ingredient-list,
.cs-benefit-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.cs-ingredient-list li,
.cs-benefit-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    color: var(--bodyTextColor);
    position: relative;
    padding-left: 1.5rem;
}

.cs-ingredient-list li:before,
.cs-benefit-list li:before {
    content: "•";
    color: var(--primary);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Mobile Responsive Adjustments */
@media only screen and (max-width: 47.9375rem) {
    .cs-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .cs-button-solid {
        width: 100%;
        min-width: auto;
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .cs-button-outline {
        width: 100%;
        min-width: auto;
    }
}

/* Tablet and Desktop */
@media only screen and (min-width: 48rem) {
    .cs-content {
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: start;
    }

    .cs-image-gallery {
        justify-content: flex-start;
    }

    .cs-product-title {
        font-size: 3rem;
    }

    .cs-actions {
        flex-direction: row;
        align-items: center;
    }
}

@media only screen and (min-width: 64rem) {
    .cs-content {
        gap: 5rem;
    }
}
</style>

<script>
// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.cs-tab-button');
    const tabPanels = document.querySelectorAll('.cs-tab-panel');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));
            
            // Add active class to clicked button and corresponding panel
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
    
    // Gallery image switching
    const galleryItems = document.querySelectorAll('.cs-gallery-item');
    const mainImage = document.querySelector('.cs-main-image img');
    
    galleryItems.forEach(item => {
        item.addEventListener('click', () => {
            const newSrc = item.querySelector('img').src;
            mainImage.src = newSrc;
        });
    });
});
</script>
