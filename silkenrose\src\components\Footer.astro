<!-- ============================================ -->
<!--                   Footer                     -->
<!-- ============================================ -->
---
import Logo from '../assets/PureluxusLogo.png';
---

<footer id="cs-footer-711">
    <div class="cs-container">
        <!--Top Section-->
        <div class="cs-top">
            <!--Warpped logo in wrapper div so that div can be 100% width to force the lists below it to stay in their line.  The cs-logo can maintain its' max width. we didn't want it 100% width becuase then the blank area to the right of the logo will also result in a click and hover event. -->
            <div class="cs-logo-wrapper">
                <a href="/" class="cs-logo">
                    <img src={Logo.src} alt="Pureluxus Logo" aria-hidden="true" loading="lazy" decoding="async" width="210" height="auto">
                </a>
            </div>
            <ul class="cs-ul">
                <li class="cs-li">
                    <span class="cs-header">Quick Links</span>
                </li>
                <li class="cs-li">
                    <a href="/about" class="cs-link">About</a>
                </li>
                <li class="cs-li">
                    <a href="/productoverview" class="cs-link">Products</a>
                </li>
                <li class="cs-li">
                    <a href="/contactsection" class="cs-link">Contact</a>
                </li>
            </ul>
            <ul class="cs-ul cs-contact-ul">
                <li class="cs-li">
                    <span class="cs-header">Contact</span>
                </li>
                <li class="cs-li">
                    <a href="tel:************" class="cs-link">
                        <img class="cs-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fphone-stroke-grey1.svg" alt="icon" loading="lazy" decoding="async" width="24" height="24" aria-hidden="true">
                        +****************
                    </a>
                </li>
                <li class="cs-li">
                    <a href="mailto:<EMAIL>" class="cs-link">
                        <img class="cs-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fmail-stroke-grey1.svg" alt="icon" loading="lazy" decoding="async" width="24" height="24" aria-hidden="true">
                        <EMAIL>
                    </a>
                </li>
                <li class="cs-li">
                    <a href="" class="cs-link" target="_blank" rel="noopener">
                        <img class="cs-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fpin-stroke-grey1.svg" alt="icon" loading="lazy" decoding="async" width="24" height="24" aria-hidden="true">
                        121 King St, Melbourne VIC 3000, Australia 
                    </a>
                </li>
            </ul>
        </div>
        <!--Middle Section-->
        <div class="cs-middle">
            <!--Navigation-->
            <ul class="cs-nav">
                <li class="cs-nav-li">
                    <a href="/" class="cs-nav-link">Home</a>
                </li>
                <li class="cs-nav-li">
                    <a href="/about" class="cs-nav-link">About</a>
                </li>
                <li class="cs-nav-li">
                    <a href="/productoverview" class="cs-nav-link">Products</a>
                </li>
                <li class="cs-nav-li">
                    <a href="/contactsection" class="cs-nav-link">Contact</a>
                </li>
            </ul>
            <!--Social-->
            <ul class="cs-social">
                <li class="cs-social-li">
                    <a href="" class="cs-social-link" aria-label="" target="_blank" rel="noopener">
                        <img class="cs-social-icon cs-default" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Ffacebook-grey.svg" alt="icon" loading="lazy" decoding="async" width="24" height="24" aria-hidden="true">
                    </a>
                </li>
                <li class="cs-social-li">
                    <a href="" class="cs-social-link" aria-label="" target="_blank" rel="noopener">
                        <img class="cs-social-icon cs-default" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Ftwitter-grey.svg" alt="icon" loading="lazy" decoding="async" width="24" height="24" aria-hidden="true">
                    </a>
                </li>
                <li class="cs-social-li">
                    <a href="" class="cs-social-link" aria-label="" target="_blank" rel="noopener">
                        <img class="cs-social-icon cs-default" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Finstagram-grey.svg" alt="icon" loading="lazy" decoding="async" width="24" height="24" aria-hidden="true">
                    </a>
                </li>
                <li class="cs-social-li">
                    <a href="" class="cs-social-link" aria-label="" target="_blank" rel="noopener">
                        <img class="cs-social-icon cs-default" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fyoutube-grey.svg" alt="icon" loading="lazy" decoding="async" width="24" height="24" aria-hidden="true">
                    </a>
                </li>
            </ul>
        </div>
        <!--Bottom Section-->
        <div class="cs-bottom">
            <span class="cs-copyright">
                © Copyright 2024 - <a href="/" class="cs-copyright-link">Pureluxus</a>
            </span>
        </div>
    </div>
</footer>

<style>/*-- -------------------------- -->
<---          Footer            -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #cs-footer-711 {
        padding: var(--sectionPadding);
        padding-bottom: 3.125rem;
    }

    /* Reduce top padding on mobile for tighter spacing with ActionBanner */
    @media only screen and (max-width: 47.9375rem) {
        #cs-footer-711 {
            padding-top: 2rem;
        }
    }
    #cs-footer-711 .cs-container {
        width: 100%;
        max-width: 80rem;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    #cs-footer-711 .cs-logo-wrapper {
        width: 100%;
    }
    #cs-footer-711 .cs-logo {
        width: 100%;
        max-width: 13.125rem;
        height: auto;
        margin-right: auto;
        display: block;
    }
    #cs-footer-711 .cs-logo img {
        width: 100%;
        height: auto;
    }
    #cs-footer-711 .cs-top {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        /* 64px - 100px */
        column-gap: clamp(4rem, 9vw, 6.25rem);
        row-gap: 2rem;
    }
    #cs-footer-711 .cs-ul {
        margin: 0;
        padding: 0;
        width: auto;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        flex: none;
    }
    #cs-footer-711 .cs-contact-ul {
        /* we remove the :before animated underline from the contact list and replace it with an underline.  The animated line doesn't work well on this section */
        width: 100%;
        max-width: 100%;
        gap: 0.75rem;
    }
    #cs-footer-711 .cs-contact-ul .cs-link:hover {
        text-decoration: underline;
    }
    #cs-footer-711 .cs-contact-ul .cs-link:before {
        display: none;
    }
    #cs-footer-711 .cs-li {
        list-style: none;
        margin: 0;
        display: block;
    }
    #cs-footer-711 .cs-header {
        font-family: 'Poppins', sans-serif;
        font-size: 1rem;
        line-height: 1.2em;
        font-weight: 600;
        margin: 0 0 1.5rem 0;
        color: var(--headerColor);
        display: block;
        letter-spacing: 0.01em;
    }
    #cs-footer-711 .cs-link {
        /* 14px - 16px */
        font-size: clamp(0.875rem, 1.5vw, 1rem);
        line-height: 1.5em;
        text-decoration: none;
        margin: 0;
        color: var(--bodyTextColor);
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        position: relative;
    }
    #cs-footer-711 .cs-link:hover:before {
        width: 100%;
    }
    #cs-footer-711 .cs-link:before {
        /* on hover underline */
        content: "";
        width: 0%;
        height: 1px;
        background: currentColor;
        opacity: 1;
        position: absolute;
        display: block;
        bottom: 0;
        left: 0;
        transition: width 0.3s;
    }
    #cs-footer-711 .cs-block {
        display: block;
    }
    #cs-footer-711 .cs-icon {
        width: 1.5rem;
        height: auto;
        margin: -0.0625rem 0.75rem 0 0;
    }
    #cs-footer-711 .cs-middle {
        width: 100%;
        /* changes at tablet */
        max-width: 28.125rem;
        /* 60px - 100px */
        margin: clamp(3.75rem, 8vw, 6.25rem) 0 0 0;
        /* 24px - 40px */
        padding-bottom: clamp(1.25rem, 5vw, 2.5rem);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }
    #cs-footer-711 .cs-nav {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        column-gap: 2.5rem;
        row-gap: 0.5rem;
    }
    #cs-footer-711 .cs-nav-li {
        list-style: none;
    }
    #cs-footer-711 .cs-nav-link {
        /* 14px - 16px */
        font-size: clamp(0.875rem, 1.5vw, 1rem);
        line-height: 1.5em;
        text-decoration: none;
        margin: 0;
        color: var(--bodyTextColor);
        position: relative;
    }
    #cs-footer-711 .cs-nav-link:hover:before {
        width: 100%;
    }
    #cs-footer-711 .cs-nav-link:before {
        /* on hover underline */
        content: "";
        width: 0%;
        height: 1px;
        background: currentColor;
        opacity: 1;
        position: absolute;
        display: block;
        bottom: 0;
        left: 0;
        transition: width 0.3s;
    }
    #cs-footer-711 .cs-social {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
    }
    #cs-footer-711 .cs-social-li {
        list-style: none;
    }
    #cs-footer-711 .cs-social-link {
        width: 2rem;
        height: 2rem;
        background-color: transparent;
        border: 1px solid var(--bodyTextColor);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.3s;
    }
    #cs-footer-711 .cs-social-link:hover {
        background-color: var(--bodyTextColor);
    }
    #cs-footer-711 .cs-social-link:hover .cs-social-icon {
        filter: grayscale(1) brightness(2000%);
    }
    #cs-footer-711 .cs-social-icon {
        width: 0.75rem;
        height: auto;
        display: block;
    }
    #cs-footer-711 .cs-hover {
        display: none;
    }
    #cs-footer-711 .cs-bottom {
        width: 100%;
        margin: 0;
        /* 24px - 40px, matches the cs-middle padding bottom */
        padding: clamp(1.25rem, 5vw, 2.5rem) 0 0 0;
        border-top: 1px solid rgba(186, 186, 186, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
    }
    #cs-footer-711 .cs-copyright {
        /* 14px - 16px */
        font-size: clamp(0.875rem, 1.5vw, 1rem);
        line-height: 1.5em;
        text-align: center;
        width: 70%;
        margin: 0;
        color: var(--bodyTextColor);
        display: block;
    }
    #cs-footer-711 .cs-copyright-link {
        font-size: inherit;
        line-height: inherit;
        text-decoration: none;
        margin: 0;
        color: inherit;
        display: inline-block;
    }
    #cs-footer-711 .cs-copyright-link:hover {
        text-decoration: underline;
    }
}
/* inbetween - 600px */
@media only screen and (min-width: 37.5rem) {
    #cs-footer-711 .cs-top {
        justify-content: flex-end;
    }
    #cs-footer-711 .cs-ul {
        width: auto;
    }
    #cs-footer-711 .cs-contact-ul {
        width: 15rem;
    }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #cs-footer-711 .cs-top {
        flex-wrap: nowrap;
    }
    #cs-footer-711 .cs-logo-wrapper {
        width: auto;
        margin-right: auto;
    }
    #cs-footer-711 .cs-middle {
        max-width: 100%;
    }
}
/* Small Desktop 1024px */
@media only screen and (min-width: 64rem) {
    #cs-footer-711 .cs-middle {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}
