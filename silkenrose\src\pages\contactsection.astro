---
import Welcome from '../components/Welcome.astro';
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import Contact from '../components/Contact.astro';
import Footer from '../components/Footer.astro';

// Welcome to Astro! Wondering what to do next? Check out the Astro documentation at https://docs.astro.build
// Don't want to use any of this? Delete everything in this file, the `assets`, `components`, and `layouts` directories, and start fresh.
---

<Layout>
	<Navigation/>
    <Contact/>
    <Footer/>

</Layout>

<!-- Import Google Fonts for modern cosmetics website -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">
<style>:root {
    /* Peach-themed color palette perfect for cosmetics website */
    /* Add these styles to your global stylesheet, which is used across all site pages. You only need to do this once. All elements in the library derive their variables and base styles from this central sheet, simplifying site-wide edits. For instance, if you want to modify how your h2's appear across the site, you just update it once in the global styles, and the changes apply everywhere. */
    --primary: #FF9A8B;        /* Soft peach - main brand color */
    --primaryLight: #FFB5A7;   /* Light peach - hover states and accents */
    --secondary: #F4A6CD;      /* Soft pink - secondary actions */
    --secondaryLight: #F8C2D4; /* Light pink - subtle highlights */
    --headerColor: #2D1B1B;    /* Dark brown - elegant headers */
    --bodyTextColor: #5D4E4E;  /* Medium brown - readable body text */
    --bodyTextColorWhite: #FFF8F6; /* Warm white - text on dark backgrounds */
    /* 13px - 16px */
    --topperFontSize: clamp(0.8125rem, 1.6vw, 1rem);
    /* 31px - 49px */
    --headerFontSize: clamp(1.9375rem, 3.9vw, 3.0625rem);
    --bodyFontSize: 1rem;
    /* 60px - 100px top and bottom */
    --sectionPadding: clamp(3.75rem, 7.82vw, 6.25rem) 1rem;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
}

*, *:before, *:after {
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
}
.cs-topper {
    font-family: 'Cormorant Garamond', serif;
    font-size: var(--topperFontSize);
    line-height: 1.2em;
    text-transform: uppercase;
    text-align: inherit;
    letter-spacing: .15em;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 0.25rem;
    display: block;
    font-style: italic;
}

.cs-title {
    font-family: 'Playfair Display', serif;
    font-size: var(--headerFontSize);
    font-weight: 700;
    line-height: 1.1em;
    text-align: inherit;
    max-width: 43.75rem;
    margin: 0 0 1rem 0;
    color: var(--headerColor);
    position: relative;
    letter-spacing: -0.02em;
}

.cs-text {
    font-family: 'Poppins', sans-serif;
    font-size: var(--bodyFontSize);
    font-weight: 400;
    line-height: 1.6em;
    text-align: inherit;
    width: 100%;
    max-width: 40.625rem;
    margin: 0;
    color: var(--bodyTextColor);
    letter-spacing: 0.01em;
}
</style>