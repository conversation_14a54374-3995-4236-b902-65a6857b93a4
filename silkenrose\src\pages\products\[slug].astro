---
import Layout from '../../layouts/Layout.astro';
import Navigation from '../../components/Navigation.astro';
import Footer from '../../components/Footer.astro';
import ProductDetail from '../../components/ProductDetail.astro';
import { products } from '../../data/products.js';

export async function getStaticPaths() {
  return products.map((product) => ({
    params: { slug: product.slug },
    props: { product },
  }));
}

const { product } = Astro.props;
---

<Layout>
  <Navigation/>
  <ProductDetail product={product} />
  <Footer/>
</Layout>

<!-- Import Google Fonts for modern cosmetics website -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">

<style>
:root {
    /* Peach-themed color palette perfect for cosmetics website */
    --primary: #FF9A8B;        /* Soft peach - main brand color */
    --primaryLight: #FFB5A7;   /* Light peach - hover states and accents */
    --secondary: #F4A6CD;      /* Soft pink - secondary actions */
    --secondaryLight: #F8C2D4; /* Light pink - subtle highlights */
    --headerColor: #2D1B1B;    /* Dark brown - elegant headers */
    --bodyTextColor: #5D4E4E;  /* Medium brown - readable body text */
    --bodyTextColorWhite: #FFF8F6; /* Warm white - text on dark backgrounds */
    /* 13px - 16px */
    --topperFontSize: clamp(0.8125rem, 1.6vw, 1rem);
    /* 31px - 49px */
    --headerFontSize: clamp(1.9375rem, 3.9vw, 3.0625rem);
    --bodyFontSize: 1rem;
    /* 60px - 100px top and bottom */
    --sectionPadding: clamp(3.75rem, 7.82vw, 6.25rem) 1rem;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
}

*, *:before, *:after {
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
}

.cs-topper {
    font-family: 'Cormorant Garamond', serif;
    font-size: var(--topperFontSize);
    line-height: 1.2em;
    text-transform: uppercase;
    text-align: inherit;
    letter-spacing: .15em;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 0.25rem;
    display: block;
    font-style: italic;
}

.cs-title {
    font-family: 'Playfair Display', serif;
    font-size: var(--headerFontSize);
    font-weight: 700;
    line-height: 1.1em;
    text-align: inherit;
    max-width: 43.75rem;
    margin: 0 0 1rem 0;
    color: var(--headerColor);
    position: relative;
    letter-spacing: -0.02em;
}

.cs-text {
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    text-align: inherit;
    width: 100%;
    max-width: 40.625rem;
    margin: 0;
    color: var(--bodyTextColor);
}

.cs-button-solid {
    font-size: 1rem;
    font-weight: 700;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    text-align: center;
    min-width: 9.375rem;
    margin: 0;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    padding: 0 1.5rem;
    color: #fff;
    background-color: var(--primary);
    border-radius: 0.25rem;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    transition: color 0.3s;
}

.cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #000;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.25rem;
    transition: width 0.3s;
}

.cs-button-solid:hover {
    color: #fff;
}

.cs-button-solid:hover:before {
    width: 100%;
}
</style>
